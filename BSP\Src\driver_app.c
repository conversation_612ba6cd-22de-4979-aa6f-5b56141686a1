/* Includes ------------------------------------------------------------------*/
#include "driver_app.h"

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/* Private function prototypes -----------------------------------------------*/
static uint32_t Speed_To_PWM(int8_t speed);
static int8_t Motor_ValidateParams(Motor_t *motor);

/* Exported functions --------------------------------------------------------*/

Motor_t right_motor; // 右电机实体
Motor_t left_motor;  // 左电机实体

void Motor_Init(void)
{
    // 右电机：PA13 (TIMG0_CCP1) + PB25 (方向控制)
    // AT8236控制：IN1=PWM(PA13)控制速度, IN2=GPIO(PB25)控制方向
    // 正转：IN1=PWM, IN2=0；反转：IN1=PWM, IN2=1
    Motor_Create(&right_motor,
                 TIMG0,                    // 定时器实例
                 DL_TIMER_CC_1_INDEX,      // CCP1通道
                 GPIOB,                    // PB端口
                 DL_GPIO_PIN_25,           // PB25方向控制引脚
                 1);                       // 反装

    // 左电机：PA12 (TIMG0_CCP0) + PB6 (方向控制)
    // AT8236控制：IN1=PWM(PA12)控制速度, IN2=GPIO(PB6)控制方向
    // 正转：IN1=PWM, IN2=0；反转：IN1=PWM, IN2=1
    Motor_Create(&left_motor,
                 TIMG0,                    // 定时器实例
                 DL_TIMER_CC_0_INDEX,      // CCP0通道
                 GPIOB,                    // PB端口
                 DL_GPIO_PIN_6,            // PB6方向控制引脚
                 0);                       // 正装

    // 启动PWM定时器
    DL_TimerG_startCounter(TIMG0);
}


/**
 * @brief 创建电机实体
 */
int8_t Motor_Create(Motor_t *motor,
                    GPTIMER_Regs *timer_inst,
                    DL_TIMER_CC_INDEX cc_index,
                    GPIO_Regs *dir_port,
                    uint32_t dir_pin,
                    uint8_t reverse)
{
    // 参数检查
    if (motor == NULL || timer_inst == NULL || dir_port == NULL)
    {
        return -1;
    }

    // 初始化硬件配置
    motor->hw.timer_inst = timer_inst;
    motor->hw.cc_index = cc_index;
    motor->hw.dir_port = dir_port;
    motor->hw.dir_pin = dir_pin;

    // 初始化电机状态
    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;
    motor->enable = 1;
    motor->reverse = reverse;

    // 设置初始状态：停止（DIR=0, PWM=0）
    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index);

    return 0;
}

/**
 * @brief 设置电机速度
 */
int8_t Motor_SetSpeed(Motor_t *motor, int8_t speed)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    // 速度范围检查
    if (speed < MOTOR_SPEED_MIN || speed > MOTOR_SPEED_MAX)
    {
        return -1;
    }

    // 检查电机是否使能
    if (!motor->enable)
    {
        return -1;
    }

    // 保存速度值
    motor->speed = speed;

    // 处理停止
    if (speed == 0)
    {
        // AT8236停止逻辑：IN1=0(PWM), IN2=0(方向)
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin); // IN2=0
        DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index); // IN1=0
        motor->state = MOTOR_STATE_STOP;
        return 0;
    }

    // AT8236控制逻辑（简化方案）：
    // 使用IN2作为方向控制，IN1(PWM)作为速度控制
    // 正转：IN1=PWM, IN2=0
    // 反转：IN1=PWM, IN2=1
    // reverse参数：反装电机需要将速度取反

    int8_t actual_speed = motor->reverse ? -speed : speed;  // 反装电机速度取反
    uint32_t pwm_value = Speed_To_PWM(abs(actual_speed));

    // 设置PWM速度（IN1）
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, pwm_value, motor->hw.cc_index);

    // 设置方向（IN2）
    if (actual_speed < 0)
    {
        // 反转：IN2=1
        DL_GPIO_setPins(motor->hw.dir_port, motor->hw.dir_pin);
        motor->state = MOTOR_STATE_BACKWARD;
    }
    else
    {
        // 正转：IN2=0
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin);
        motor->state = MOTOR_STATE_FORWARD;
    }

    return 0;
}

/**
 * @brief 停止电机
 */
int8_t Motor_Stop(Motor_t *motor)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    // AT8236停止逻辑：IN1=0(PWM), IN2=0(方向)
    DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin); // IN2=0
    DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index); // IN1=0

    // 更新状态
    motor->speed = 0;
    motor->state = MOTOR_STATE_STOP;

    return 0;
}

/**
 * @brief 电机刹车
 */
void Motor_brake(Motor_t *motor)
{
    // AT8236刹车：IN1=1, IN2=1 (但这需要两个PWM，简化为停止)
    // 简化实现：直接停止电机
    Motor_Stop(motor);
}

/**
 * @brief 获取电机状态
 */
MotorState_t Motor_GetState(Motor_t *motor)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return MOTOR_STATE_ERROR;
    }

    return motor->state;
}

/**
 * @brief 使能/失能电机
 */
int8_t Motor_Enable(Motor_t *motor, uint8_t enable)
{
    // 参数检查
    if (Motor_ValidateParams(motor) != 0)
    {
        return -1;
    }

    motor->enable = enable;

    // 如果失能，立即停止电机
    if (!enable)
    {
        // AT8236停止逻辑：IN1=0(PWM), IN2=0(方向)
        DL_GPIO_clearPins(motor->hw.dir_port, motor->hw.dir_pin); // IN2=0
        DL_TimerG_setCaptureCompareValue(motor->hw.timer_inst, 0, motor->hw.cc_index); // IN1=0
        motor->speed = 0;
        motor->state = MOTOR_STATE_STOP;
    }

    return 0;
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 将速度值转换为PWM比较值
 * @param speed: 速度值 (-100 到 +100)
 * @retval PWM比较值 (0 到 MOTOR_PWM_PERIOD)
 */
static uint32_t Speed_To_PWM(int8_t speed)
{
    uint8_t abs_speed;

    // 获取速度绝对值
    if (speed < 0)
    {
        abs_speed = (uint8_t)(-speed);
    }
    else
    {
        abs_speed = (uint8_t)speed;
    }

    // 如果速度为0，直接返回0
    if (abs_speed == 0)
    {
        return 0;
    }

    // 转换为PWM值：速度百分比 * PWM周期
    uint32_t pwm_value = (uint32_t)abs_speed * MOTOR_PWM_PERIOD / 100;

    // PWM阈值提升逻辑：如果计算出的PWM小于阈值，提升到阈值
    if (pwm_value > 0 && pwm_value < MOTOR_MIN_PWM_THRESHOLD)
    {
        pwm_value = MOTOR_MIN_PWM_THRESHOLD;
    }

    // 确保不超过最大值
    if (pwm_value > MOTOR_PWM_PERIOD)
    {
        pwm_value = MOTOR_PWM_PERIOD;
    }

    return pwm_value;
}

/**
 * @brief 验证电机参数有效性
 * @param motor: 电机实体指针
 * @retval 0: 有效, -1: 无效
 */
static int8_t Motor_ValidateParams(Motor_t *motor)
{
    if (motor == NULL ||
        motor->hw.timer_inst == NULL ||
        motor->hw.dir_port == NULL)
    {
        return -1;
    }

    return 0;
}



